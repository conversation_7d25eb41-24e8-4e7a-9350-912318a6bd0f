// defines the protocol for the onboard data


// {
//     "type": "device-identification",
//     "data": {
//         "device_id": "1234567890",
//         "device_classifier": "device-classifier",
//     }
// }

// {
//     "type": "journey",
//     "data": { // reuse onboard.rs::Journey
//         "trip_id": "1234567890",
//         "destination_text": "destination-text",
//         "line_name": "line-name",
//         "calls": [
//             {
//                 "stop_id": "1234567890",
//                 "seq_number": 1,
//                 "scheduled_arrival_time": 1234567890,
//                 "scheduled_departure_time": 1234567890,
//                 "actual_arrival_time": 1234567890,
//                 "actual_departure_time": 1234567890,
//                 "destination_text": "destination-text",
//                 "platform_name": "platform-name",
//                 "stop_name": "stop-name",
//             }  
//         ],
//         "current_call_index": 1,
//         "status": "before-stop",
//     }
// }

// {
//     "type": "vehicle-information",
//     "data": { // reuse onboard.rs::VehicleInformation
//         "vehicle_id": "1234567890",
//         "coach_number": "coach-number",
//         "door_status": "open",
//     }
// }

use serde::{Deserialize, Serialize};

use crate::onboard_model::{OnboardData, OnboardDeviceIdentification};


#[derive(Serialize, Deserialize, Debug, Clone, PartialEq)]
#[serde(tag = "type", rename = "data")]
pub enum OnboardProtocolMessage {
    #[serde(rename = "device-identification")]
    DeviceIdentification (
        OnboardDeviceIdentification,
    ),
    #[serde(rename = "onboard-data")]
    OnboardData (
        OnboardData,
    ),
}
