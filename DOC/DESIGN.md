# Architecture and Design decisions log

## Onbaord data server can start in one of two modes

### Simulation mode
Instantiates a simulation worker and opens a http api.

### Standalone protocol mode


## Onboard Data Server serves provi

The Onboard Data Server is able to detect context of a device and provide tailored information for the device.

The data model in the protocol is designed to convey present a single display perspective.



```
 
 |                         train                              |
          | a cars group           | |   another cars group   |
  ______   ______   ______   ______   ______   ______   ______ 
 /  __  |.|  __  |.|  __  |.|  __  |.|  __  |.|  __  |.|  __  |
 `^^  ^^' '^^  ^^' '^^  ^^' '^^  ^^' '^^  ^^' '^^  ^^' '^^  ^^'
          | car  |  or                        | coach|        

EMU - train consist/ train set
 |                         train                                 |
 |  EMU                         | |            EMU               |
  ______  ______  ______  ______   ______  ______  ______  ______  
 /  __  ||  __  ||  __  ||  __  \_/  __  ||  __  ||  __  ||  __  \ 
 `^^  ^^''^^  ^^''^^  ^^''^^  ^^' `^^  ^^''^^  ^^''^^  ^^''^^  ^^' 
          | car  |  or                        | coach|        
```



## Data preparation pipeline


- device identification
- vehicle data source

Device identificatio is a process during which onboard-data-server identifies device details such as its mounting orientation, point and provides that informatio back to devicce.

Vehicle data source provides all the information about vehicle from a vheicle-server persepctive.
- current tirp/jrouney status
- train set composition
- vehicle status (e.g. doors)


High level architecture (seen from the network, and processes view, not the onboard data sercer intenral architecture)
1. Device connects to the server over websocket
2. server identifies the device and sends back to the device identification info, effectively creating a device context bound to the connection

3. if Onboard data server has Onboard data it sends it out to the newly connected Outputs (devices)


4. Onbaord data comes to the onboard data server (whatever protocol, input source)

5. onbard data server processes the data and fans it out to the outputs (e.g. Device connections)

6. data is saved in the state so for each newly connected device it can be quickly processed for the new context and sent



------


The application can start in two modes:
Single protocol mode. Where the configuration chosen protocol is directly wired as an input
Interactive mode. Where TUI or Web in the future is used to manually pick input, control/modify it on the fly and feed the core for further processing.



Data domains:
- Journey information
- Vehicle information
- Vehicle signals

Data inputs:
- 
- 

Input:
 - TUI:
   - can load Journey or baselink trip from file
   - can load trip from baselink this needs a search/load menu
   - can modify some aspects of Calls (like set canceleld, or delayd)
   - can set autoplay mode wtih speed up
   - can move states and current call index by moving time
   - 
 - baselink mode (with assigned vehicle id)
 - file mode.
 - another protocol like IBIS-IP


DevicesConfigurationService:
is an interface to retrieve onboardDeviceIDentification data by providing the input to match.
Device configuration service offers configuration loading from string (json).
for TUI/ webUI purposes we might want some dynamic configuratipon managemenet options, to CRUD entries in the configuration


Core:
It receivs Journey Information, Vehicle information and vehicle signals
Currentl it doesn't have to be an Active object.

When core receives a change it might do some processing on it, but generally sends it out to the Outputs.

Outputs are:
 - OnboardDataProtocol
 - maybe print out or file

---------
Display process

1. WebDisplay loads itself from the onbaord-dispay-server
2. WebDisplay Connectes to onboard-display-server over WS and gets it's OnboardDeviceIdentification
3. Webdisplay then reports itself back with its display_id and OnbardDeviceIdentification
4. server returns back the layout assigned to it
5. If layout is different or have to updated the WebDisplay reloads

6. if layouts is up to date, continues to normal state of the connection.




### Dynamic device configuration change

If configuration changes all active connections shall be reevaluated to check wether the OnboardDeviceInformation has not chhanged for them. If it was changed the `device-identification` message shall be sent.
No need for reconnections, just send the device.
The WebDisplay Client will handle the messagea accordingly. will reload and restart.



### WebUI and TUI functionality

Note! Both kinds of UI use the input data as a way to load the data, but doesn't monitor the sources for changes.

- Management of the device assignments
- Input data selection (loading)
- Main data operation and control