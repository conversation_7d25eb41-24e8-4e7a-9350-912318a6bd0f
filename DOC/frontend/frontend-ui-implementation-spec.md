# Web UI Implementation Specification

## Overview

This document specifies the implementation of a React + TypeScript web frontend for the Onboard Data Server (ODIS) that will replace the current TUI. The web UI is designed around two main navigation sections and integrates with the existing HTTP API endpoints.

**Project Location:** `ui/` directory within the main project

## Technology Stack

- **Framework:** React 18 with TypeScript
- **Build Tool:** Vite for fast development and optimized builds
- **UI Library:** Material-UI (MUI) v5 - well-established, accessible, comprehensive
- **State Management:** Zustand (lightweight, TypeScript-friendly)
- **HTTP Client:** Axios with TypeScript support
- **WebSocket:** Native WebSocket API for real-time updates
- **Styling:** MUI's emotion-based styling system

## Application Architecture

### Project Structure
```
ui/
├── package.json
├── vite.config.ts
├── tsconfig.json
├── index.html
├── src/
│   ├── App.tsx                     # Main app component with routing
│   ├── main.tsx                    # Entry point
│   ├── components/
│   │   ├── Layout/
│   │   │   ├── AppLayout.tsx       # Main layout with sidebar
│   │   │   ├── Sidebar.tsx         # Left navigation menu
│   │   │   └── Header.tsx          # Top header bar
│   │   ├── TripLoading/
│   │   │   ├── TripLoadingPage.tsx # Main trip loading page
│   │   │   ├── VehicleSelector.tsx # Vehicle-based selection
│   │   │   ├── TripIdInput.tsx     # Direct trip ID input
│   │   │   ├── TripPreview.tsx     # Trip preview component
│   │   │   └── ConnectionStatus.tsx# Baselink status display
│   │   ├── Simulation/
│   │   │   ├── SimulationPage.tsx  # Main simulation dashboard
│   │   │   ├── PlaybackControls.tsx# Play/pause/speed controls
│   │   │   ├── TimeControls.tsx    # Time navigation
│   │   │   ├── NavigationControls.tsx # State/stop jumping
│   │   │   ├── SimulationState.tsx # Current state display
│   │   │   └── JourneyProgress.tsx # Journey overview
│   │   └── Common/
│   │       ├── LoadingSpinner.tsx  # Loading states
│   │       ├── ErrorDisplay.tsx    # Error handling
│   │       └── Toast.tsx           # Notifications
│   ├── hooks/
│   │   ├── useApi.ts               # HTTP API integration
│   │   ├── useWebSocket.ts         # WebSocket connection
│   │   ├── useSimulation.ts        # Simulation state management
│   │   └── useBaselink.ts          # Baselink operations
│   ├── services/
│   │   ├── api.ts                  # HTTP API client
│   │   ├── websocket.ts            # WebSocket service
│   │   └── types.ts                # API type definitions
│   ├── store/
│   │   ├── simulationStore.ts      # Simulation state
│   │   ├── baselinkStore.ts        # Baselink data
│   │   └── uiStore.ts              # UI state
│   ├── utils/
│   │   ├── formatters.ts           # Data formatting
│   │   └── constants.ts            # App constants
│   └── theme.ts                    # MUI theme configuration
└── dist/                           # Build output
```

## User Interface Design

### Main Layout Structure

#### Sidebar Navigation (Left Menu)
The application features a persistent left sidebar with two main navigation items:

**1. Trip Loading** 🚌
- **Vehicle Selection** - Select vehicle to load its current trip
- **Trip ID Input** - Enter trip ID directly to fetch trip data

**2. Simulation** ▶️
- **Simulation Control & State View** - Complete simulation dashboard

### Page Specifications

#### 1. Trip Loading Page (`TripLoadingPage.tsx`)

**Layout:** Two-tab interface for different loading methods

##### Tab 1: Vehicle Selection
```
┌─ Connection Status ─────────────────────────────────┐
│ 🟢 Connected to Baselink | Version 1.2.3           │
│ ↻ Refresh Connection                                │
└─────────────────────────────────────────────────────┘

┌─ Vehicle List ──────────────────────────────────────┐
│ Vehicle ID    │ Trip ID      │ Status              │
│ vehicle_123   │ Bus 123      │ Active              │
│ vehicle_456   │ Bus 456      │ Active              │
│ [↻ Refresh List]                                   │
└─────────────────────────────────────────────────────┘

┌─ Trip Preview ──────────────────────────────────────┐
│ Trip ID: 422250752                                  │
│ Line: 3 → Altwarmbüchen                           │
│ Stops: 15 total                                     │
│ [Load Trip into Simulation]                        │
└─────────────────────────────────────────────────────┘
```

**Components:**
- **ConnectionStatus:** Shows baselink connection state, version info, reconnect option
- **VehicleSelector:** Data table with vehicle list, selection handling, refresh functionality
- **TripPreview:** Trip details display with load action

##### Tab 2: Trip ID Input
```
┌─ Trip ID Input ─────────────────────────────────────┐
│ Trip ID: [_________________] [Get Trip]             │
│                                                     │
│ ┌─ Trip Preview ────────────────────────────────────┐
│ │ Trip ID: 422250752                               │
│ │ Line: 3 → Altwarmbüchen                        │
│ │ Stops: 15 total                                  │
│ │ [Load Trip into Simulation]                     │
│ └──────────────────────────────────────────────────┘
└─────────────────────────────────────────────────────┘
```

**Components:**
- **TripIdInput:** Simple form with text input and fetch button
- **TripPreview:** Reused component from vehicle selection tab

#### 2. Simulation Control & State Page (`SimulationPage.tsx`)

**Layout:** Multi-panel dashboard organized in grid layout

```
┌─ Playback Controls ──────┬─ Time Controls ──────────┐
│ Status: ⏸️ Stopped       │ Current Time:            │
│ Speed: [4x ▼]            │ 14:32:15                 │
│ [▶️ Resume] [⏸️ Stop]    │ Set Time: [HH:MM:SS] [⚡] │
└──────────────────────────┴───────────────────────────┘

┌─ Navigation Controls ────┬─ Journey Progress ───────┐
│ States: [◀️] 3/8 [▶️]     │ Trip: Bus 123           │
│ Stops:  [◀️] 5/15 [▶️]    │ Line 3 → Altwarmbüchen │
│                          │ ████████▓▓▓▓▓▓▓ 53%     │
└──────────────────────────┴───────────────────────────┘

┌─ Current State ──────────┬─ Stop Timeline ──────────┐
│ 📍 Wettbergen (Platform SB2) │ 🔵 Hauptbahnhof      │
│ Status: At Stop          │ ⚪ Steintor             │
│ Arrival: 14:30 (on time) │ 🟡 Wettbergen (current) │
│ Next: Steintor in 3 mins │ ⚪ Altwarmbüchen       │
└──────────────────────────┴───────────────────────────┘
```

**Panel Components:**

1. **PlaybackControls:**
   - Current playback state indicator (Playing/Stopped)
   - Speed multiplier dropdown (1x-10x)
   - Resume/Stop buttons with visual feedback

2. **TimeControls:**
   - Large current timestamp display
   - Manual time input with set button
   - Independent of playback state

3. **NavigationControls:**
   - State navigation (Previous/Next State with counter)
   - Stop navigation (Previous/Next Stop with counter)
   - Clear visual indicators of current position

4. **JourneyProgress:**
   - Trip metadata (ID, line, destination)
   - Progress bar showing journey completion
   - High-level status information

5. **SimulationState:**
   - Current stop name and platform
   - Location status (approaching, at stop, departed)
   - Timing information (scheduled vs actual)
   - Next stop preview

6. **StopTimeline:**
   - Scrollable list of all stops
   - Current stop highlighted
   - Status indicators for each stop
   - Time information where available

## API Integration

### HTTP API Endpoints (Based on Current Implementation)

The frontend integrates with the following HTTP API endpoints:

**Baselink Domain:**
- `GET /api/baselink/status` - Connection status and version
- `GET /api/baselink/vehicles` - List available vehicles  
- `GET /api/baselink/trips/{trip_id}` - Get trip by ID

**Simulation Domain:**
- `GET /api/simulation/state` - Get complete simulation state
- `POST /api/simulation/stop` - Stop playback
- `POST /api/simulation/resume` - Resume with optional speed
- `POST /api/simulation/time` - Set explicit timestamp
- `POST /api/simulation/next-state` - Jump to next state
- `POST /api/simulation/previous-state` - Jump to previous state
- `POST /api/simulation/next-stop` - Jump to next stop
- `POST /api/simulation/previous-stop` - Jump to previous stop

**Data Domain:**
- `POST /api/data/load/journey` - Load journey into simulation

## Development Configuration

### Package.json
```json
{
  "name": "odis-web-ui",
  "version": "1.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "lint": "eslint src --ext ts,tsx",
    "type-check": "tsc --noEmit"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.15.0",
    "@mui/material": "^5.14.0",
    "@mui/icons-material": "^5.14.0",
    "@emotion/react": "^11.11.0",
    "@emotion/styled": "^11.11.0",
    "zustand": "^4.4.0",
    "axios": "^1.5.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "@vitejs/plugin-react": "^4.0.0",
    "eslint": "^8.45.0",
    "eslint-plugin-react-hooks": "^4.6.0",
    "typescript": "^5.0.2",
    "vite": "^4.4.0"
  }
}
```

### Vite Configuration
```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
      },
    },
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
  },
})
```

### TypeScript Configuration
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

## Development Workflow

### Setup Steps
1. **Create project:** `cd ui && npm create vite@latest . --template react-ts`
2. **Install dependencies:** `npm install` (add MUI, Zustand, etc.)
3. **Start development:** `npm run dev` (runs on port 3000)
4. **Backend integration:** Vite proxy automatically forwards `/api` requests to `localhost:8080`

### Build Process
1. **Development:** `npm run dev` - Hot reload, proxy to backend
2. **Build:** `npm run build` - TypeScript compilation + Vite build
3. **Output:** Static files in `ui/dist/` ready for Rust binary embedding

## Error Handling & UX

### Error States
- **Network errors:** Toast notifications with retry options
- **API errors:** Display error messages from backend API responses
- **Connection loss:** Persistent notification with auto-reconnect
- **Validation errors:** Inline form validation

### Loading States
- **API calls:** Button loading spinners during requests
- **Data fetching:** Skeleton loaders for tables and lists
- **Page transitions:** Page-level loading indicators

### User Feedback
- **Toast notifications:** Success/error messages for actions
- **Visual state changes:** Active buttons, selected items
- **Real-time updates:** WebSocket connection status
- **Confirmation dialogs:** For potentially destructive actions

## Production Integration

The built frontend (`ui/dist/`) will be embedded in the Rust binary using `rust-embed`:

```rust
#[derive(RustEmbed)]
#[folder = "ui/dist/"]
struct WebAssets;

// Serve SPA with fallback to index.html
// Handle static assets with proper MIME types
```

This creates a single binary deployment with the web UI embedded, maintaining the project's single-binary deployment model while providing a modern web interface.

## Implementation Priority

### Phase 1: Foundation
1. Setup Vite + React + TypeScript project
2. Basic layout with sidebar navigation
3. API service layer and error handling
4. Basic routing between two main pages

### Phase 2: Trip Loading
1. Connection status display
2. Vehicle selection interface
3. Trip ID input interface  
4. Trip preview component
5. Integration with baselink API endpoints

### Phase 3: Simulation Control
1. Simulation state display
2. Playback controls implementation
3. Time controls and navigation
4. Journey progress visualization
5. Real-time WebSocket updates

### Phase 4: Polish & Production
1. Error handling and edge cases
2. Loading states and UX improvements
3. Build optimization for embedding
4. Documentation and testing

This specification provides a complete roadmap for implementing the web UI that matches the existing HTTP API and provides the two-menu structure requested.
