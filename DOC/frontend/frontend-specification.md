# Frontend Web UI Specification

## Project Overview

This document specifies the implementation of a React + TypeScript web frontend for the Onboard Passenger Information Display Data Server (ODIS). The web UI will replace the current TUI (Terminal User Interface) and provide an intuitive, modern interface for testing and simulation of onboard journey data.

## Architecture Overview

### Technology Stack

- **Frontend Framework:** React 18 with TypeScript
- **Build Tool:** Vite (fast development and optimized production builds)
- **UI Framework:** Material-UI (MUI) v5 - mature, well-documented, accessible
- **State Management:** Zustand (lightweight, TypeScript-friendly)
- **HTTP Client:** Axios with TypeScript support
- **WebSocket:** Native WebSocket API with automatic reconnection
- **Styling:** MUI's emotion-based styling + custom CSS modules
- **Development:** ESLint, Prettier, TypeScript strict mode

### Project Structure

```
ui/
├── package.json
├── vite.config.ts
├── tsconfig.json
├── index.html
├── src/
│   ├── App.tsx                     # Main application component
│   ├── main.tsx                    # Application entry point
│   ├── components/
│   │   ├── layout/
│   │   │   ├── AppLayout.tsx       # Main layout with sidebar
│   │   │   ├── Sidebar.tsx         # Navigation sidebar
│   │   │   └── Header.tsx          # Application header
│   │   ├── baselink/
│   │   │   ├── BaselinkPage.tsx    # Main baselink page
│   │   │   ├── VehicleSelector.tsx # Vehicle-based trip selection
│   │   │   ├── TripIdInput.tsx     # Trip ID input component
│   │   │   ├── TripPreview.tsx     # Trip details preview
│   │   │   └── ConnectionStatus.tsx # Baselink connection status
│   │   ├── simulation/
│   │   │   ├── SimulationPage.tsx  # Main simulation control page
│   │   │   ├── PlaybackControls.tsx # Play/stop/speed controls
│   │   │   ├── TimeControls.tsx    # Time navigation controls
│   │   │   ├── JumpControls.tsx    # State/stop navigation
│   │   │   ├── SimulationState.tsx # Current state display
│   │   │   └── JourneyOverview.tsx # Journey progress visualization
│   │   ├── common/
│   │   │   ├── LoadingSpinner.tsx  # Loading indicator
│   │   │   ├── ErrorBoundary.tsx   # Error handling
│   │   │   ├── Toast.tsx           # Toast notifications
│   │   │   └── ConfirmDialog.tsx   # Confirmation dialogs
│   │   └── data/
│   │       ├── StopList.tsx        # Stop timeline display
│   │       ├── CallDetails.tsx     # Current call information
│   │       └── JourneyInfo.tsx     # Trip metadata display
│   ├── hooks/
│   │   ├── useApi.ts               # Generic API hook
│   │   ├── useWebSocket.ts         # WebSocket connection hook
│   │   ├── useSimulation.ts        # Simulation state management
│   │   └── useBaselink.ts          # Baselink operations hook
│   ├── services/
│   │   ├── api.ts                  # HTTP API client
│   │   ├── websocket.ts            # WebSocket service
│   │   └── types.ts                # API type definitions
│   ├── store/
│   │   ├── simulationStore.ts      # Simulation state store
│   │   ├── baselinkStore.ts        # Baselink data store
│   │   └── appStore.ts             # Global application state
│   ├── utils/
│   │   ├── formatters.ts           # Time/data formatting utilities
│   │   ├── validation.ts           # Input validation helpers
│   │   └── constants.ts            # Application constants
│   └── styles/
│       ├── globals.css             # Global styles
│       └── theme.ts                # MUI theme configuration
├── public/
│   ├── favicon.ico
│   └── index.html
└── dist/                           # Build output (generated)
```

## User Interface Design

### Layout Structure

#### Main Layout (`AppLayout.tsx`)
- **Left Sidebar (240px wide):** Primary navigation with two main sections
- **Main Content Area:** Dynamic content based on selected navigation item
- **Header Bar:** Application title, connection status, and user actions

#### Sidebar Navigation (`Sidebar.tsx`)
Two main navigation sections:

1. **Baselink Trip Selection**
   - Icon: Trip/Vehicle icon
   - Label: "Trip Loading"
   - Expandable subsections:
     - "Vehicle Selection" - Vehicle-based loading
     - "Trip ID Input" - Direct trip ID entry

2. **Simulation Control**
   - Icon: Play/Control icon  
   - Label: "Simulation"
   - Single page with multiple control panels

### Page Specifications

#### 1. Baselink Trip Selection Page (`BaselinkPage.tsx`)

**Layout:** Tabbed interface with two tabs:

##### Tab 1: Vehicle Selection
- **Connection Status Panel:**
  - Baselink server connection indicator (connected/disconnected)
  - Server version and uptime information
  - Reconnect button if disconnected

- **Vehicle List Section:**
  - Data table with columns: Vehicle ID, Trip ID, Status
  - Refresh button to reload vehicle list
  - Loading state when fetching vehicles
  - Error handling with retry option

- **Trip Preview Section:**
  - Shows when a vehicle is selected
  - Displays: Trip ID, Line Name, Destination, Number of Stops
  - Stop list with basic information (name, scheduled times)
  - "Load Trip" button to import into simulation

##### Tab 2: Trip ID Input
- **Simple Input Form:**
  - Text input field for Trip ID
  - "Get Trip" button to fetch trip data
  - Validation for empty/invalid input

- **Trip Preview Section:**
  - Same preview component as vehicle selection tab
  - Shows trip details once fetched successfully
  - "Load Trip" button to import into simulation

**Error Handling:**
- Toast notifications for API errors
- Inline validation messages
- Graceful handling of baselink connection issues

#### 2. Simulation Control Page (`SimulationPage.tsx`)

**Layout:** Multi-panel dashboard with the following sections:

##### Playback Control Panel (`PlaybackControls.tsx`)
- **Current State Indicator:**
  - Large status badge: "Playing" (green) / "Stopped" (gray)
  - Current speed multiplier display

- **Control Buttons:**
  - Stop/Resume toggle button (primary action)
  - Speed selector dropdown (1x to 10x, default 4x)
  - Visual feedback on button states

##### Time Control Panel (`TimeControls.tsx`)
- **Current Time Display:**
  - Large, prominent timestamp display
  - Format: "HH:MM:SS" with date
  
- **Manual Time Input:**
  - Time picker component
  - "Set Time" button
  - Independent of playback state

##### Navigation Control Panel (`JumpControls.tsx`)
- **State Navigation:**
  - "Previous State" button
  - "Next State" button
  - State counter: "State X of Y"

- **Stop Navigation:**
  - "Previous Stop" button  
  - "Next Stop" button
  - Stop counter: "Stop X of Y"

##### Journey Overview Panel (`JourneyOverview.tsx`)
- **Trip Information:**
  - Trip ID, Line Name, Destination
  - Total stops, current position
  - Journey status indicator

- **Progress Visualization:**
  - Horizontal progress bar showing journey completion
  - Current stop highlighted
  - Timeline with major stops marked

##### Current State Panel (`SimulationState.tsx`)
- **Current Location:**
  - Stop name and platform
  - Location status (approaching, at stop, departed)
  - Schedule vs actual time comparison

- **Next Stop Preview:**
  - Next stop name and scheduled arrival
  - Distance/time estimate if available

##### Stop Timeline (`StopList.tsx`)
- **Complete Stop List:**
  - Scrollable list of all stops in journey
  - Current stop highlighted
  - Scheduled vs actual times
  - Status indicators (on time, delayed, early)

**Real-time Updates:**
- WebSocket connection status indicator
- Auto-refresh simulation state every second
- Visual feedback for all state changes

## State Management

### Zustand Store Structure

#### Simulation Store (`simulationStore.ts`)
```typescript
interface SimulationState {
  // Current simulation state
  journey: Journey | null;
  currentCallIndex: number | null;
  status: LocationStatus | null;
  timestamp: number;
  playbackState: 'playing' | 'stopped';
  speedMultiplier: number;
  
  // WebSocket connection
  isConnected: boolean;
  connectionError: string | null;
  
  // Actions
  updateState: (state: SimulationStateResponse) => void;
  setPlaybackState: (state: 'playing' | 'stopped') => void;
  setSpeedMultiplier: (speed: number) => void;
  setTimestamp: (timestamp: number) => void;
}
```

#### Baselink Store (`baselinkStore.ts`)
```typescript
interface BaselinkState {
  // Connection status
  connectionStatus: BaselinkConnectionStatus;
  isConnected: boolean;
  
  // Vehicles and trips
  vehicles: VehicleInfo[];
  selectedVehicle: VehicleInfo | null;
  selectedTrip: Journey | null;
  
  // Loading states
  vehiclesLoading: boolean;
  tripLoading: boolean;
  
  // Actions
  setVehicles: (vehicles: VehicleInfo[]) => void;
  selectVehicle: (vehicle: VehicleInfo) => void;
  setSelectedTrip: (trip: Journey) => void;
  setConnectionStatus: (status: BaselinkConnectionStatus) => void;
}
```

#### App Store (`appStore.ts`)
```typescript
interface AppState {
  // UI state
  currentPage: 'baselink' | 'simulation';
  sidebarCollapsed: boolean;
  
  // Notifications
  toasts: Toast[];
  
  // Actions
  setCurrentPage: (page: string) => void;
  toggleSidebar: () => void;
  addToast: (toast: Toast) => void;
  removeToast: (id: string) => void;
}
```

## API Integration

### HTTP API Service (`api.ts`)

```typescript
class ApiService {
  private baseURL = '/api';
  
  // Baselink endpoints
  async getBaselinkStatus(): Promise<BaselinkStatus>;
  async getVehicles(): Promise<VehicleInfo[]>;
  async getTripById(tripId: string): Promise<Journey>;
  
  // Simulation endpoints
  async getSimulationState(): Promise<SimulationState>;
  async stopPlayback(): Promise<void>;
  async resumePlayback(speedMultiplier?: number): Promise<void>;
  async setTime(timestamp: number): Promise<void>;
  async nextState(): Promise<void>;
  async previousState(): Promise<void>;
  async nextStop(): Promise<void>;
  async previousStop(): Promise<void>;
  
  // Data loading endpoints
  async loadJourney(journey: Journey): Promise<void>;
}
```

### WebSocket Integration (`websocket.ts`)

```typescript
class WebSocketService {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  
  connect(onMessage: (data: any) => void): void;
  disconnect(): void;
  private reconnect(): void;
  
  // Event handlers
  onOpen: () => void;
  onClose: () => void;
  onError: (error: Event) => void;
  onMessage: (data: any) => void;
}
```

## Component Specifications

### Shared Components

#### LoadingSpinner (`LoadingSpinner.tsx`)
- MUI CircularProgress with overlay
- Configurable size and message
- Used throughout the application for async operations

#### ErrorBoundary (`ErrorBoundary.tsx`)
- React error boundary with fallback UI
- Error reporting and retry functionality
- User-friendly error messages

#### Toast Notifications (`Toast.tsx`)
- MUI Snackbar-based notifications
- Success, error, warning, and info variants
- Auto-dismiss with configurable duration
- Queue management for multiple toasts

#### ConfirmDialog (`ConfirmDialog.tsx`)
- MUI Dialog for user confirmations
- Used for destructive actions
- Customizable title, message, and button labels

### Styling and Theming

#### MUI Theme Configuration (`theme.ts`)
```typescript
const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2', // Professional blue
    },
    secondary: {
      main: '#dc004e', // Accent red
    },
    background: {
      default: '#f5f5f5',
      paper: '#ffffff',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h5: {
      fontWeight: 600,
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none', // Keep normal case
        },
      },
    },
  },
});
```

#### Responsive Design
- Mobile-first approach with MUI breakpoints
- Sidebar collapses to drawer on mobile
- Touch-friendly button sizes
- Readable font sizes across devices

## Development Setup

### Package Configuration (`package.json`)

```json
{
  "name": "odis-web-ui",
  "version": "1.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "lint:fix": "eslint src --ext ts,tsx --fix",
    "type-check": "tsc --noEmit"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "@mui/material": "^5.14.0",
    "@mui/icons-material": "^5.14.0",
    "@emotion/react": "^11.11.0",
    "@emotion/styled": "^11.11.0",
    "axios": "^1.5.0",
    "zustand": "^4.4.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "@vitejs/plugin-react": "^4.0.0",
    "eslint": "^8.45.0",
    "eslint-plugin-react-hooks": "^4.6.0",
    "eslint-plugin-react-refresh": "^0.4.0",
    "typescript": "^5.0.2",
    "vite": "^4.4.0"
  }
}
```

### Vite Configuration (`vite.config.ts`)

```typescript
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
      },
      '/ws': {
        target: 'ws://localhost:8080',
        ws: true,
      },
    },
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    minify: 'esbuild',
  },
})
```

### TypeScript Configuration (`tsconfig.json`)

```json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

## Development Workflow

### Local Development
1. **Start Backend:** `cargo run` (from project root)
2. **Start Frontend:** `cd ui && npm run dev`
3. **Access Application:** http://localhost:3000
4. **API Proxy:** Automatic proxy to backend at localhost:8080

### Build Process
1. **Type Check:** `npm run type-check`
2. **Lint:** `npm run lint`
3. **Build:** `npm run build`
4. **Output:** Static files in `ui/dist/` ready for embedding

### Code Quality
- **ESLint:** Enforces code style and catches common errors
- **TypeScript:** Strict mode for type safety
- **Prettier:** Code formatting (integrated with ESLint)
- **Git Hooks:** Pre-commit linting and type checking

## Error Handling and UX

### Error States
- **Network Errors:** Toast notifications with retry options
- **Validation Errors:** Inline form validation messages
- **API Errors:** User-friendly error messages with error codes
- **Connection Loss:** Persistent notification with reconnect attempts

### Loading States
- **Skeleton Loaders:** For data-heavy components
- **Button Loading:** Spinner in buttons during async operations
- **Page Loading:** Full-page loading for initial data
- **Progressive Loading:** Load non-critical data after initial render

### User Feedback
- **Toast Notifications:** Success, error, warning, info messages
- **Visual State Changes:** Button states, active selections
- **Progress Indicators:** For long-running operations
- **Confirmation Dialogs:** For destructive actions

## Future Enhancements

### Phase 1 Extensions
- **Export Functionality:** Download current simulation state
- **Advanced Time Controls:** Date picker, time range selection
- **Trip History:** Recently loaded trips list
- **Keyboard Shortcuts:** Power user navigation

### Phase 2 Features
- **Map Visualization:** Route display with stop markers
- **Real-time Metrics:** Performance monitoring dashboard
- **Multi-trip Comparison:** Side-by-side trip analysis
- **Custom Themes:** Light/dark mode toggle

### Integration Features
- **File Upload:** Drag-and-drop trip data files
- **Trip Templates:** Save and reuse common trip configurations
- **Advanced Filtering:** Search and filter trips by multiple criteria
- **Collaboration:** Share trip states via URLs

## Production Deployment

### Asset Embedding
The built frontend will be embedded in the Rust binary using `rust-embed`:

```rust
#[derive(RustEmbed)]
#[folder = "ui/dist/"]
struct WebAssets;

// Serve index.html for SPA routes
// Serve static assets with proper MIME types
// Handle 404s gracefully
```

### Single Binary Deployment
- Frontend assets embedded in Rust binary
- No external dependencies for web UI
- Automatic fallback to index.html for SPA routing
- Production-optimized builds with compression

This specification provides a comprehensive roadmap for implementing a modern, user-friendly web interface that maintains all the functionality of the current TUI while providing significant improvements in usability and visual feedback. 