# HTTP API Testing Guide

This guide provides comprehensive curl examples for testing all HTTP API functionality of the onboard data server.

## Prerequisites

### 1. Start the Server

#### For Manual Journey Testing (HTTP Data Source)
```bash
cargo run -- --data-source http --vehicle-id test-vehicle-123
```

#### For Baselink Integration Testing
```bash
cargo run -- --data-source baselink --baselink-url https://your-baselink-server:50051 --vehicle-id your-vehicle-id
```

The server will start:
- HTTP API server on `http://localhost:8080`
- WebSocket server on `ws://localhost:8765` (for real-time data streaming)

**Note:** When using `--data-source baselink`, the baselink endpoints will be functional. When using `--data-source http`, the baselink endpoints will return `503 Service Unavailable` errors.

### 2. Test Journey File

Create `test_journey.json` with realistic journey data:

```json
{
  "journey": {
    "tripId": "bus-line-7-trip-42",
    "destinationText": "Central Station",
    "lineName": "Line 7",
    "calls": [
      {
        "stopId": "stop_001",
        "seqNumber": 1,
        "scheduledArrivalTime": 1735300800,
        "scheduledDepartureTime": 1735300800,
        "actualArrivalTime": null,
        "actualDepartureTime": 1735300805,
        "destinationText": "Central Station",
        "platformName": "A1",
        "stopName": "Airport Terminal",
        "advisoryMessages": ["Welcome aboard"],
        "isCancelled": false,
        "exitSide": "Right"
      },
      {
        "stopId": "stop_002",
        "seqNumber": 2,
        "scheduledArrivalTime": 1735301100,
        "scheduledDepartureTime": 1735301160,
        "actualArrivalTime": null,
        "actualDepartureTime": null,
        "destinationText": "Central Station",
        "platformName": "B2",
        "stopName": "Business District",
        "advisoryMessages": [],
        "isCancelled": false,
        "exitSide": "Left"
      },
      {
        "stopId": "stop_003",
        "seqNumber": 3,
        "scheduledArrivalTime": 1735301400,
        "scheduledDepartureTime": 1735301460,
        "actualArrivalTime": null,
        "actualDepartureTime": null,
        "destinationText": "Central Station",
        "platformName": "C1",
        "stopName": "University Campus",
        "advisoryMessages": ["Next stop: Central Station"],
        "isCancelled": false,
        "exitSide": "Right"
      },
      {
        "stopId": "stop_004",
        "seqNumber": 4,
        "scheduledArrivalTime": 1735301700,
        "scheduledDepartureTime": 1735301760,
        "actualArrivalTime": null,
        "actualDepartureTime": null,
        "destinationText": "Central Station",
        "platformName": "D3",
        "stopName": "Shopping Mall",
        "advisoryMessages": [],
        "isCancelled": false,
        "exitSide": "Left"
      },
      {
        "stopId": "stop_005",
        "seqNumber": 5,
        "scheduledArrivalTime": 1735302000,
        "scheduledDepartureTime": 1735302000,
        "actualArrivalTime": null,
        "actualDepartureTime": null,
        "destinationText": "Central Station",
        "platformName": "E1",
        "stopName": "Central Station",
        "advisoryMessages": ["Final destination", "Thank you for traveling with us"],
        "isCancelled": false,
        "exitSide": "Right"
      }
    ]
  }
}
```

## API Endpoints

### Baselink Integration

#### Get Baselink Connection Status
```bash
curl -X GET http://localhost:8080/api/baselink/status | python3 -m json.tool
```

**Response (when baselink is available):**
```json
{
  "success": true,
  "data": {
    "connected": true,
    "version": "1.2.3",
    "server_url": null,
    "startup_time": 1735300000
  },
  "message": "Baselink connection status retrieved successfully",
  "timestamp": "2025-06-21T11:00:00.000000Z",
  "error": null
}
```

**Response (when baselink is not configured):**
```json
{
  "success": false,
  "data": null,
  "message": "Baselink API is not configured",
  "timestamp": "2025-06-21T11:00:00.000000Z",
  "error": {
    "code": "BASELINK_NOT_AVAILABLE",
    "message": "Baselink API is not configured",
    "details": "Server was not started with baselink data source"
  }
}
```

#### Get Available Vehicles
```bash
curl -X GET http://localhost:8080/api/baselink/vehicles | python3 -m json.tool
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "vehicle_123",
      "tripId": "Bus 123",
      "vehicleType": null,
      "status": "active"
    },
    {
      "id": "vehicle_456",
      "tripId": "Bus 456",
      "vehicleType": null,
      "status": "active"
    }
  ],
  "message": "Vehicles retrieved successfully",
  "timestamp": "2025-06-21T11:00:00.000000Z",
  "error": null
}
```

**Error Response (communication failure):**
```json
{
  "success": false,
  "data": null,
  "message": "Failed to retrieve vehicles from baselink",
  "timestamp": "2025-06-21T11:00:00.000000Z",
  "error": {
    "code": "BASELINK_COMMUNICATION_ERROR",
    "message": "Failed to retrieve vehicles from baselink",
    "details": "Connection timeout or network error details"
  }
}
```

#### Get Trip Information by ID
```bash
curl -X GET http://localhost:8080/api/baselink/trips/422250752 | python3 -m json.tool
```

**Response:**
```json
{
  "success": true,
  "data": {
    "tripId": "422250752",
    "operatingDay": 1749592800,
    "destinationText": "Altwarmbüchen",
    "lineId": "14148",
    "lineName": "3",
    "directionId": "1",
    "halts": [
      {
        "stopId": "de:03241:5181:1:5182",
        "seqNumber": 0,
        "scheduledArrivalTime": 1749649440,
        "scheduledDepartureTime": 1749649440,
        "actualArrivalTime": null,
        "actualDepartureTime": 1749649449,
        "stopName": "Wettbergen",
        "platformName": "SB2"
      },
      {
        "stopId": "de:03241:5183:1:5184",
        "seqNumber": 1,
        "scheduledArrivalTime": 1749649500,
        "scheduledDepartureTime": 1749649500,
        "actualArrivalTime": null,
        "actualDepartureTime": null,
        "stopName": "Hauptbahnhof",
        "platformName": "A1"
      }
    ]
  },
  "message": "Trip data retrieved successfully",
  "timestamp": "2025-06-21T11:00:00.000000Z",
  "error": null
}
```

**Error Response (trip not found):**
```json
{
  "success": false,
  "data": null,
  "message": "Trip with ID 'invalid_trip_id' not found",
  "timestamp": "2025-06-21T11:00:00.000000Z",
  "error": {
    "code": "TRIP_NOT_FOUND",
    "message": "Trip with ID 'invalid_trip_id' not found",
    "details": "Trip may not be active or may not exist"
  }
}
```

#### Converting Trip Response for Journey Loading

The get_trip API returns data in an API response wrapper, but the load journey endpoint expects the journey data to be wrapped in a `journey` object. Use this `jq` transformation to convert the format:

```bash
# Get trip data and convert for journey loading
curl -s http://localhost:8080/api/baselink/trips/422250752 | jq '{"journey": .data}' > journey_for_loading.json

# Then load it into the simulation
curl -X POST http://localhost:8080/api/data/load/journey \
  -H "Content-Type: application/json" \
  -d @journey_for_loading.json
```

**One-liner to fetch and load a trip:**
```bash
# Fetch trip data, convert format, and load into simulation
curl -s http://localhost:8080/api/baselink/trips/422250752 | jq '{"journey": .data}' | \
  curl -X POST http://localhost:8080/api/data/load/journey \
    -H "Content-Type: application/json" \
    -d @-
```

This transforms the API response:
```json
{
  "success": true,
  "data": { /* journey data */ }
}
```

Into the expected load journey format:
```json
{
  "journey": { /* journey data */ }
}
```

### Data Loading

#### Load Journey
```bash
curl -X POST http://localhost:8080/api/data/load/journey \
  -H "Content-Type: application/json" \
  -d @test_journey.json
```

**Response:**
```json
{
  "success": true,
  "data": null,
  "message": "Journey loaded successfully",
  "timestamp": "2025-06-21T11:00:43.001134Z",
  "error": null
}
```

#### Get Available Data Sources
```bash
curl -X GET http://localhost:8080/api/data/sources | python3 -m json.tool
```

**Response:**
```json
{
  "success": true,
  "data": {
    "current_source": "http",
    "available_sources": ["http", "baselink", "file", "manual"]
  },
  "message": "Data sources retrieved",
  "timestamp": "2025-06-21T11:03:07.265128Z",
  "error": null
}
```

### Simulation Control

#### Get Current Simulation State
```bash
curl -X GET http://localhost:8080/api/simulation/state | python3 -m json.tool
```

**Response includes:**
- Complete journey data with all stops
- Current call index and status
- Current timestamp
- Playback state (`stopped`/`playing`)
- Speed multiplier (1-10)

**Example Response:**
```json
{
  "success": true,
  "data": {
    "journey": { /* complete journey data */ },
    "currentCallIndex": 1,
    "status": "BetweenStops",
    "timestamp": 1735300900,
    "playbackState": "stopped",
    "speedMultiplier": 2
  },
  "message": "Current simulation state",
  "timestamp": "2025-06-21T11:00:53.258912Z",
  "error": null
}
```

#### Start/Resume Playback
```bash
# Resume with default speed
curl -X POST http://localhost:8080/api/simulation/resume

# Resume with custom speed (1-10)
curl -X POST http://localhost:8080/api/simulation/resume \
  -H "Content-Type: application/json" \
  -d '{"speed_multiplier": 2}'
```

**Response:**
```json
{
  "success": true,
  "data": null,
  "message": "Playback resumed",
  "timestamp": "2025-06-21T11:01:03.767519Z",
  "error": null
}
```

#### Stop Playback
```bash
curl -X POST http://localhost:8080/api/simulation/stop
```

**Response:**
```json
{
  "success": true,
  "data": null,
  "message": "Playback stopped",
  "timestamp": "2025-06-21T11:01:21.836244Z",
  "error": null
}
```

#### Set Specific Time
```bash
curl -X POST http://localhost:8080/api/simulation/time \
  -H "Content-Type: application/json" \
  -d '{"timestamp": 1735300900}'
```

**Response:**
```json
{
  "success": true,
  "data": null,
  "message": "Time updated",
  "timestamp": "2025-06-21T11:01:31.014715Z",
  "error": null
}
```

#### Navigate States (Fine Control)
```bash
# Move to next state
curl -X POST http://localhost:8080/api/simulation/next-state

# Move to previous state
curl -X POST http://localhost:8080/api/simulation/previous-state
```

States progress: `BetweenStops` → `BeforeStop` → `AtStop` → `AfterStop` → `BetweenStops`

#### Navigate Stops (Coarse Control)
```bash
# Jump to next stop
curl -X POST http://localhost:8080/api/simulation/next-stop

# Jump to previous stop
curl -X POST http://localhost:8080/api/simulation/previous-stop
```

## Complete Testing Workflows

### Baselink API Testing Workflow

Here's a comprehensive baselink testing sequence:

```bash
echo "=== 1. CHECK BASELINK STATUS ==="
curl -X GET http://localhost:8080/api/baselink/status | python3 -c "
import json, sys
try:
    data = json.load(sys.stdin)
    if data['success']:
        status = data['data']
        print(f'Baselink Status: {\"Connected\" if status[\"connected\"] else \"Disconnected\"}')
        if status['version']:
            print(f'Version: {status[\"version\"]}')
        if status['startup_time']:
            print(f'Startup Time: {status[\"startup_time\"]}')
    else:
        print(f'Baselink Error: {data[\"error\"][\"code\"]} - {data[\"message\"]}')
except:
    print('Failed to parse response')
"

echo -e "\n=== 2. GET AVAILABLE VEHICLES ==="
curl -X GET http://localhost:8080/api/baselink/vehicles | python3 -c "
import json, sys
try:
    data = json.load(sys.stdin)
    if data['success']:
        vehicles = data['data']
        print(f'Found {len(vehicles)} vehicles:')
        for vehicle in vehicles:
            print(f'  - Vehicle {vehicle[\"id\"]}: Trip {vehicle[\"tripId\"]} ({vehicle[\"status\"]})')
    else:
        print(f'Error: {data[\"error\"][\"code\"]} - {data[\"message\"]}')
except:
    print('Failed to parse response')
"

echo -e "\n=== 3. GET SPECIFIC TRIP (replace with actual trip ID) ==="
TRIP_ID="422250752"  # Replace with actual trip ID from vehicles response
curl -X GET "http://localhost:8080/api/baselink/trips/$TRIP_ID" | python3 -c "
import json, sys
try:
    data = json.load(sys.stdin)
    if data['success']:
        trip = data['data']
        print(f'Trip: {trip[\"tripId\"]} - Line {trip[\"lineName\"]}')
        print(f'Destination: {trip[\"destinationText\"]}')
        print(f'Stops: {len(trip[\"halts\"])}')
        print('First few stops:')
        for halt in trip['halts'][:3]:
            print(f'  {halt[\"seqNumber\"]}: {halt[\"stopName\"]} ({halt[\"platformName\"]})')
    else:
        print(f'Error: {data[\"error\"][\"code\"]} - {data[\"message\"]}')
except:
    print('Failed to parse response')
"

echo -e "\n=== 4. TEST NON-EXISTENT TRIP ==="
curl -X GET http://localhost:8080/api/baselink/trips/non_existent_trip_id | python3 -c "
import json, sys
try:
    data = json.load(sys.stdin)
    if not data['success']:
        print(f'Expected Error: {data[\"error\"][\"code\"]} - {data[\"message\"]}')
    else:
        print('Unexpected success for non-existent trip')
except:
    print('Failed to parse response')
"
```

### Manual Journey Testing Workflow

Here's a comprehensive testing sequence for manual journey control:

```bash
echo "=== 1. LOAD JOURNEY ==="
curl -X POST http://localhost:8080/api/data/load/journey \
  -H "Content-Type: application/json" \
  -d @test_journey.json

echo -e "\n=== 2. GET INITIAL STATE ==="
curl -X GET http://localhost:8080/api/simulation/state | python3 -m json.tool

echo -e "\n=== 3. SET TIME TO BEGINNING ==="
curl -X POST http://localhost:8080/api/simulation/time \
  -H "Content-Type: application/json" \
  -d '{"timestamp": 1735300700}'

echo -e "\n=== 4. START PLAYBACK (3x SPEED) ==="
curl -X POST http://localhost:8080/api/simulation/resume \
  -H "Content-Type: application/json" \
  -d '{"speed_multiplier": 3}'

echo -e "\n=== 5. CHECK PLAYBACK STATE ==="
curl -X GET http://localhost:8080/api/simulation/state | python3 -c "
import json, sys
data = json.load(sys.stdin)
state = data['data']
print(f'Playback: {state[\"playbackState\"]} at {state[\"speedMultiplier\"]}x speed')
print(f'Current Stop: {state[\"journey\"][\"calls\"][state[\"currentCallIndex\"]][\"stopName\"]}')
print(f'Status: {state[\"status\"]}')
"

echo -e "\n=== 6. STOP PLAYBACK ==="
curl -X POST http://localhost:8080/api/simulation/stop

echo -e "\n=== 7. JUMP TO NEXT STOP ==="
curl -X POST http://localhost:8080/api/simulation/next-stop

echo -e "\n=== 8. MOVE TO NEXT STATE ==="
curl -X POST http://localhost:8080/api/simulation/next-state

echo -e "\n=== 9. MOVE TO PREVIOUS STATE ==="
curl -X POST http://localhost:8080/api/simulation/previous-state

echo -e "\n=== 10. JUMP TO PREVIOUS STOP ==="
curl -X POST http://localhost:8080/api/simulation/previous-stop

echo -e "\n=== 11. GET FINAL STATE ==="
curl -X GET http://localhost:8080/api/simulation/state | python3 -c "
import json, sys
data = json.load(sys.stdin)
state = data['data']
print(f'Final Position: Call {state[\"currentCallIndex\"]} - {state[\"journey\"][\"calls\"][state[\"currentCallIndex\"]][\"stopName\"]}')
print(f'Status: {state[\"status\"]}')
"

echo -e "\n=== 12. GET AVAILABLE SOURCES ==="
curl -X GET http://localhost:8080/api/data/sources | python3 -m json.tool
```

## Status Values

The simulation tracks four location statuses:

- **`BetweenStops`**: Traveling between stops
- **`BeforeStop`**: Approaching a stop
- **`AtStop`**: Currently at a stop
- **`AfterStop`**: Just left a stop

## Speed Multiplier

Valid range: 1-10
- `1`: Real-time speed
- `2-5`: Moderate acceleration for testing
- `6-10`: High-speed testing

## Error Handling

All endpoints return consistent error format:
```json
{
  "success": false,
  "data": null,
  "message": "Error description",
  "timestamp": "2025-06-21T11:00:00.000000Z",
  "error": "Detailed error information"
}
```

## Integration with WebSocket

The HTTP API controls the simulation, while the WebSocket server on port 8765 broadcasts real-time data updates. You can:

1. Use HTTP API to control the simulation
2. Connect WebSocket clients to receive live updates
3. Both work simultaneously

## Notes

- **Field Names**: JSON uses `camelCase` (e.g., `stopId`, `scheduledArrivalTime`)
- **Timestamps**: Unix epoch seconds (e.g., `1735300800`)
- **Concurrent Access**: Multiple HTTP clients can control the same simulation
- **State Persistence**: Simulation state persists until server restart or new journey load

## Quick Reference

| Operation | Endpoint | Method | Body |
|-----------|----------|--------|------|
| **Baselink APIs** | | | |
| Baselink Status | `/api/baselink/status` | GET | - |
| Get Vehicles | `/api/baselink/vehicles` | GET | - |
| Get Trip | `/api/baselink/trips/{trip_id}` | GET | - |
| **Data Loading** | | | |
| Load Journey | `/api/data/load/journey` | POST | Journey JSON |
| Get Sources | `/api/data/sources` | GET | - |
| **Simulation Control** | | | |
| Get State | `/api/simulation/state` | GET | - |
| Resume | `/api/simulation/resume` | POST | `{"speed_multiplier": 2}` |
| Stop | `/api/simulation/stop` | POST | - |
| Set Time | `/api/simulation/time` | POST | `{"timestamp": 1735300900}` |
| Next State | `/api/simulation/next-state` | POST | - |
| Previous State | `/api/simulation/previous-state` | POST | - |
| Next Stop | `/api/simulation/next-stop` | POST | - |
| Previous Stop | `/api/simulation/previous-stop` | POST | - | 