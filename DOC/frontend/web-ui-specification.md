# Web UI Specification

## Overview

This document specifies the implementation of a web-based user interface to replace the current TUI (Terminal User Interface) for the Onboard Passenger Information Display Data Server. The web UI will provide comprehensive testing and simulation capabilities for onboard journey data.

## Architecture Overview

The solution consists of two main components:

1. **HTTP API Layer** (Rust backend extension)
2. **Web Frontend** (TypeScript/React application)

## HTTP API Layer (Rust Backend)

### API Structure

The HTTP API will be organized into the following domains:

#### 1. Baselink Domain (`/api/baselink`)

**System Information:**
- `GET /api/baselink/status` - Get baselink server connection status and version

**Vehicle-Based Trip Loading:**
- `GET /api/baselink/vehicles` - Get list of available vehicles
- `GET /api/baselink/vehicles/{vehicle_id}/trip` - Get current trip for a specific vehicle
- `POST /api/baselink/vehicles/{vehicle_id}/trip/load` - Load vehicle's current trip into simulation

**Active Trip Search:**
- `GET /api/baselink/trips/active` - Get currently active trips with optional line filtering
  - Query params: `line_name` (optional) - Filter trips by line name
- `POST /api/baselink/trips/{trip_id}/load` - Load specific trip into simulation

#### 2. Data Loading Domain (`/api/data`)

**File Operations:**
- `POST /api/data/load/file` - Load journey data from uploaded file
  - Content-Type: `multipart/json`
  - Body: Form with file upload
  the json structure must serialize to Journey model as available in onboard_model.rs
- `GET /api/data/files` - List available data files (server side storage)
- `POST /api/data/load/file/{filename}` - Load from server-side file

**Data Sources:**
- `GET /api/data/sources` - Get available data sources and their status
- `POST /api/data/source/switch` - Switch between data sources
  - Body: `{ "source": "baselink|manual" }`


#### 3. Simulation Control Domain (`/api/simulation`)

**State Observation:**
- `GET /api/simulation/state` - Get complete simulation state
  - Returns: current Journey, current call data, timestamp, playback state (stopped/playing), speed multiplier (integer 1-10)

**Playback Control:**
- `POST /api/simulation/stop` - Stop playback at current timestamp (pause and stop are the same)
- `POST /api/simulation/resume` - Resume playback from current timestamp
  - Body: `{ "speed_multiplier": 2 }` (optional, integer 1-10)

**Time Control:**
- `POST /api/simulation/time` - Set explicit timestamp (doesn't affect playback state)
  - Body: `{ "timestamp": 1234567890 }`

**Navigation (Jump Operations):**
- `POST /api/simulation/next-state` - Jump to next trip state
- `POST /api/simulation/previous-state` - Jump to previous trip state  
- `POST /api/simulation/next-stop` - Jump to next stop (same state)
- `POST /api/simulation/previous-stop` - Jump to previous stop (same state)

#### 4. Data Output Domain (`/api/output`)

**Current State:**
- `GET /api/output/current` - Get current onboard data state
- `GET /api/output/journey` - Get current journey information

**WebSocket Connection:**
- `GET /api/output/websocket` - WebSocket endpoint for real-time updates
- Existing WebSocket functionality will be preserved

**Export:**
- `GET /api/output/export/json` - Export current state as JSON
- `GET /api/output/export/trip` - Export current trip data

### API Response Format

All API responses will follow a consistent format:

```json
{
  "success": true,
  "data": { /* response data */ },
  "message": "Operation completed successfully",
  "timestamp": "2024-01-01T10:00:00Z"
}
```

Error responses:
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": "Additional error details"
  },
  "timestamp": "2024-01-01T10:00:00Z"
}
```

### API Validation Rules

**Speed Multiplier Validation:**
- Must be an integer between 1 and 10 (inclusive)
- Invalid values return error code `INVALID_SPEED_MULTIPLIER`
- Default value: 4

### Baselink-Specific Response Examples

**Vehicle List Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "vehicle_123",
      "trip_id": "Bus 123",
      "type": "bus",
      "status": "active"
    }
  ],
  "message": "Vehicles retrieved successfully"
}
```

**Vehicle Trip Response:**
```json
{
  "success": true,
  "data": {
    "trip_id": "422250752",
    "operating_day": 1749592800,
    "destination_text": "Altwarmbüchen",
    "line_id": "14148",
    "line_name": "3",
    "direction_id": "1",
    "halts": [
      {
        "stop_id": "de:03241:5181:1:5182",
        "seq_number": 0,
        "scheduled_arrival_time": 1749649440,
        "scheduled_departure_time": 1749649440,
        "actual_arrival_time": 0,
        "actual_departure_time": 1749649449,
        "stop_name": "Wettbergen",
        "platform_name": "SB2"
      }
    ]
  },
  "message": "Trip data retrieved successfully"
}
```

**Connection Status Response:**
```json
{
  "success": true,
  "data": {
    "connected": true,
    "version": "1.2.3",
    "server_url": "https://baselink.example.com:50051",
    "startup_time": 1749649440
  },
  "message": "Baselink connection status"
}
```

**Simulation State Response:**
```json
{
  "success": true,
  "data": {
    "journey": {
      "trip_id": "422250752",
      "destination_text": "Altwarmbüchen",
      "line_name": "3",
      "calls": [
        {
          "stop_id": "de:03241:5181:1:5182",
          "seq_number": 0,
          "scheduled_arrival_time": 1749649440,
          "scheduled_departure_time": 1749649440,
          "actual_arrival_time": null,
          "actual_departure_time": 1749649449,
          "stop_name": "Wettbergen",
          "platform_name": "SB2",
          "advisory_messages": [],
          "is_cancelled": false,
          "exit_side": "Right"
        }
      ],
      "current_call_index": 2,
      "status": "AtStop"
    },
    "timestamp": 1749649800,
    "playback_state": "playing",
    "speed_multiplier": 2
  },
  "message": "Current simulation state"
}
```

## Web Frontend (TypeScript/React)

### Technology Stack

- **Framework:** React 18 with TypeScript
- **Build Tool:** Vite
- **UI Framework:** Material-UI (MUI) or Tailwind CSS + Headless UI
- **State Management:** Zustand or React Query + Context
- **WebSocket:** Native WebSocket API with reconnection logic
- **HTTP Client:** Axios or Fetch API
- **Testing:** Vitest + React Testing Library

### Application Structure

```
frontend/
├── src/
│   ├── components/
│   │   ├── common/          # Reusable UI components
│   │   ├── baselink/        # Baselink-specific components
│   │   ├── simulation/      # Simulation control components
│   │   └── data-display/    # Data visualization components
│   ├── pages/
│   │   ├── DataLoading.tsx
│   │   ├── SimulationControl.tsx
│   │   └── DataView.tsx
│   ├── hooks/               # Custom React hooks
│   ├── services/            # API service layers
│   ├── store/               # State management
│   ├── types/               # TypeScript type definitions
│   └── utils/               # Utility functions
├── public/
├── package.json
├── vite.config.ts
└── tsconfig.json
```

### Main Application Features

#### 1. Data Loading Interface

**Baselink Trip Selection:**
- **Connection Status:** Display baselink server connection status
- **Two Simple Discovery Methods:**
  - **Vehicle-Based:** Select vehicle → preview current trip → load into simulation
  - **Active Trip Search:** Browse active trips with optional line filter → select trip → load into simulation
- **Trip Preview:** Display trip details (route, stops, times) for confirmation
- **Trip Loading:** Simple load button to import selected trip into simulation

**File Upload:**
- Drag & drop file upload for trip JSON files
- File validation and preview of trip structure
- Server-side file browser for pre-loaded trip files
- Support for both internal journey format and raw baselink format

#### 2. Simulation Control Interface

**Playback Controls:**
- **Stop/Resume buttons** (no separate pause - stop and pause are the same)
- **Speed multiplier input** (set when resuming playback)
- **Current timestamp display** with manual time input
- **Playback state indicator** (stopped/playing)

**Navigation Controls:**
- **Jump Operations:**
  - Next State / Previous State buttons
  - Next Stop / Previous Stop buttons (same state)
- **Manual Time Setting:**
  - Timestamp input field (independent of playback state)

**State Display:**
- **Current Journey Info:** Trip ID, destination, line name
- **Current Location:** Call index, stop name, location status
- **Trip Progress:** Visual progress through stops

#### 3. Data Visualization

**Journey Overview:**
- Route map (if coordinates available)
- Stop timeline with current position
- Trip information summary
- Vehicle status display

**Current State Display:**
- Current stop information
- Next stop preview
- Delay/schedule information
- Call status indicators

**Real-time Updates:**
- WebSocket connection status
- Live data streaming
- Connection retry logic
- Data freshness indicators

### Development and Production Modes

#### Development Mode

**Vite Development Server:**
```javascript
// vite.config.ts
export default defineConfig({
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
      },
      '/ws': {
        target: 'ws://localhost:8080',
        ws: true,
      },
    },
  },
});
```

**Development Workflow:**
1. Start Rust backend: `cargo run`
2. Start frontend dev server: `npm run dev`
3. Frontend proxies API calls to backend
4. Hot module replacement for development

#### Production Mode

**Embedded Web Assets:**
- Frontend built as static assets
- Assets embedded in Rust binary using `rust-embed`
- Single binary deployment
- Static file serving from embedded assets

**Implementation Approach:**
```rust
// In Cargo.toml
[dependencies]
rust-embed = "6.8"
axum = "0.7"

// Static asset serving
#[derive(RustEmbed)]
#[folder = "frontend/dist/"]
struct Assets;

// Serve static files and fallback to index.html for SPA
```

## Implementation Plan

### Phase 1: HTTP API Foundation
1. Add HTTP server alongside existing WebSocket server (using Axum)
2. Implement basic API structure and response formats
3. Add CORS support for development mode
4. Implement data output endpoints

### Phase 2: Baselink Integration
1. Implement baselink API endpoints
2. Add trip search and loading functionality
3. Integrate with existing baselink client
4. Add error handling and validation

### Phase 3: Simulation Control
1. Extend existing manual data fetcher for API control
2. Implement time manipulation endpoints
3. Add modification system integration
4. Implement navigation controls

### Phase 4: Frontend Foundation
1. Set up React + TypeScript + Vite project
2. Implement basic layout and navigation
3. Create API service layer
4. Set up state management

### Phase 5: Core Features
1. Implement data loading interface
2. Create simulation control panel
3. Add data visualization components
4. Implement WebSocket integration

### Phase 6: Production Integration
1. Set up asset embedding
2. Implement single binary deployment
3. Add production build pipeline
4. Testing and optimization

### Phase 7: Advanced Features
1. Enhanced data visualization
2. Export functionality
3. Advanced modification tools
4. Performance optimization

## Technical Considerations

### Security
- Input validation on all API endpoints
- File upload size and type restrictions
- CORS configuration for development
- Rate limiting on API endpoints

### Performance
- Efficient WebSocket message handling
- Debounced API calls for real-time controls
- Lazy loading of heavy components
- Optimized bundle size for embedded deployment

### Error Handling
- Comprehensive error boundaries in React
- Graceful degradation for WebSocket failures
- User-friendly error messages
- Automatic retry mechanisms

### Testing Strategy
- Unit tests for API endpoints
- Integration tests for simulation control
- Frontend component testing
- End-to-end testing for critical flows

## Migration Strategy

1. **Parallel Development:** Build web UI alongside existing TUI
2. **Feature Parity:** Ensure web UI matches TUI functionality
3. **User Testing:** Validate UI/UX with stakeholders
4. **Gradual Migration:** Phase out TUI after web UI is stable
5. **Documentation:** Update user documentation and guides

This specification provides a comprehensive roadmap for implementing the web UI while maintaining the existing system's reliability and functionality.

## Baselink Integration Details

### Current Implementation Status

Based on the existing codebase analysis, the baselink integration supports:

**Required Operations for Web UI:**
- `get_vehicles()` - Get vehicle list for vehicle-based loading
- `get_vehicle_trip(vehicle_id)` - Get current trip for selected vehicle  
- `get_active_trips()` - Get active trips, with optional line filtering

**Data Structures:**
- **Vehicle:** Vehicle identification
- **Trip:** Contains `trip_id`, `destination_text`, `line_name`, and halts
- **Halt:** Contains stop information and timing

### Baselink Journey Loading Workflow

**Simple Workflows for Trip Loading:**

**1. Vehicle-Based Loading:**
1. Get vehicle list from baselink
2. User selects a vehicle 
3. Retrieve vehicle's current trip
4. Preview trip details and load into simulation

**2. Active Trip Loading:**
1. Get active trips from baselink
2. Optionally filter by line name
3. User selects a trip from the list
4. Load selected trip into simulation

### API Implementation Notes

**Existing Capabilities:**
- The current baselink API client (`baselink_api_client.rs`) wraps the gRPC baselink API
- Trip data conversion from baselink format to internal format is handled in `from_baselink.rs`
- Raw trip data is saved to files for debugging (`raw/baselink_trip.json`)

**Implementation Focus:**
- Simple vehicle dropdown for vehicle-based loading
- Basic active trip list with optional line filtering  
- Trip preview with essential details (route, stops, times)
- Single "Load Trip" action for both workflows
- Connection status monitoring for baselink reliability