# Onboard Display Information Service (ODIS)

The application that will be deployed on Onboard Display Information Computer unit.

- Polls/fetches data from CIS (CustomerInformationService) and status services on Vehicle Central Computer
- Serves Onboard Display Information interface for WebDisplays (protocol based on WebSocket; contents to be defined)

- Listens for and saintains session and connection state for each display client  

- Processes and normalizes vehicle/journey data
- Publishes normalized JSON events over WebSocket to displays

- **Core Modules:**  
  1. _Ingestion Layer_ (adapters to poll/fetch from CIS & status sources)  
  2. _Event Processor_ (stream processor, data fan‑out)  
  3. _API Layer_ (WebSocket endpoint)
  4. _Connection Manager_ (manages client WebSocket connections)

- supports multiple data sources
- only one data source can be enabled at a time
- data source are of different kind, protocol, data models
- however all data sources convert to the same model that is to be sent do WebDisplay over WebSocket


Implementation:

- WebSocket - tokio-tungstenite
- generally use tokio, async
- use anyhow
- jiff ( instead of chrono)
- 


Simplified architecture