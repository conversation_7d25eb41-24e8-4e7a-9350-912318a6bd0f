# Onboard Display Information Service (ODIS) — First Draft Specification

## 1. Introduction

### 1.1 Purpose  
This document defines the high‑level architecture and behavior of the Onboard Display Information Service (ODIS), a server application running on the coach/vehicle's dedicated display computer. ODIS gathers real‑time vehicle, journey and trip status from the central computer and exposes a WebSocket API to in‑coach displays.  

### 1.2 Scope  
- Describe system components and their interactions  
- Specify service endpoints, data models and configuration layers  
- Outline startup, runtime and error‑handling behavior  
- Provide a baseline for developers and system integrators  

---

## 2. Overall Architecture

```mermaid
graph TD
    subgraph "Outside Vehicle Network"
        WDS[WebDisplay Server]
    end

    subgraph "Inside Vehicle Network"
        BBDC[Browser-based Display Clients]
        ODIS["Onboard Display Information Server (ODIS)"]
        VCC[Vehicle Central Computer]
    end

    BBDC -- "(1) Load WebDisplay Frontend" --> WDS
    WDS -- "(2) Serves web app, layouts, config
    [WebSocket & HTTP(S)]" --> BBDC
    BBDC -- "(3) Connect WebSocket" --> ODIS
    ODIS -- "(4) Publishes real-time data
    [WebSocket]" --> BBDC
    ODIS -- "(5) Poll/Fetch Vehicle/Trip data" --> VCC
    VCC -- "Return data" --> ODIS
```

### Additional (optional) flow

The initial configuration of displays needs to be provided in some way.
It would be beneficial if the URL of the Display Content would be provided by IBIS-IP specific DNS-SD service.

Then the flow would be like:
1. The display device is through some method given a DeviceID (which according to VDV301 is a mounting point identifier)
2. The display device, its device management service queriese for HTMLService for an HTML Content URL 
3. The DNS-SD server responses with an URL, and coach ID
4. The Dispay management service knowing coach id, url and it's own device id, sets an given URL for the browser; The given URL is a address of Onboard Display Information Server; The SErver serves /proxies WebDisplay Server, providing two services: A WebDisplay and a onboard display Information service (via WebSockeT)
5. The Webdisplay Frontend once loaded tries to communicate to its localhost display management service (like Display HArdware API) to retrieve unique device identifier and location specific identifier of a device. This information is then used to retrieve layout and configuration information by the standard WebDisplay methods
6. After initialized and if the layout and display data assignemnts allow the WebDisplay Frontend, makes an additional WebSocket connection to the Onboard Display Information server to start retrieving onboard information

7. The information from the onboard ifnromation server is display device agnostic. Gives all data necessary for all perspectives, but is focused on Display Information

8. The WebDisplay Frontend provided with layout, data assignemnts configuration, device id, location specific id, and onboard display information data is able to process the input and provide information according to layout.

it seems that the Onboard Display Information Server shall be a proxy of the WebDisplay server.
That would make a perfect fit for the HTMLContentService of IBIS-IP
---

#### Outcome requirements after this flow

WebDisplay needs a functionality to assign templates for a category of displays represented by the Device-Mounting-Identifier
and not for the specific DIsplay id

WebDisplay URL:
https://webdispla-server/display/device-mounting-point=front&onboard-display-information-service=ws://odis/odis

if onboard-display-information-service omitted and display needs to use the onboard info it connects
to webdisplay-server host for the source


Displays in vehicles shall be LRUs. A replacement or simply reset of browser cache currently causes WebDisplay to create a new device id.
There must be a way to indentify the device uniquely by (manufacturer-assigned-coach-id,device-mounting-point)

## 3. Component Descriptions

### 3.1 Vehicle Central Computer  
- **CustomerInformationService (CIS):** Provides journey, stop list, timetable data.  
- **Status Services:** Provides real‑time door states, vehicle speed, HVAC temperatures, etc.  
- **Interface:** gRPC/REST/WebSocket endpoints for ODIS to poll/fetch data.

### 3.2 ODIS (Onboard Display Information Server)  
- **Responsibilities:**  
  - Actively polls/fetches data from CIS and status services on Vehicle Central Computer
  - Maintains session and connection state for each display client  
  - Processes and normalizes vehicle/journey data
  - Publishes normalized JSON events over WebSocket to displays
- **Core Modules:**  
  1. _Ingestion Layer_ (adapters to poll/fetch from CIS & status sources)  
  2. _Event Processor_ (stream processor, data fan‑out)  
  3. _API Layer_ (WebSocket endpoint)
  4. _Connection Manager_ (manages client WebSocket connections)

### 3.3 WebDisplay Configuration Server  
- **Role:** External server (outside vehicle network) that serves the web application frontend, layouts, and configuration
- **Provides:**
  - Progressive Web App (PWA) frontend code
  - Layout templates and widget configurations
  - Media assets (images, fonts, etc.)
  - Configuration for connecting to ODIS

### 3.4 Browser‑based Display Clients  
- **Platform:** Embedded WebView or kiosk browser  
- **Startup Flow:**  
  1. Load web application from WebDisplay Server  
  2. Receive configuration (layouts, widgets, data source URLs)  
  3. Connect to ODIS WebSocket using provided URL
  4. Render passenger information in real time using data from ODIS
  5. Optionally maintain WebSocket connection to WebDisplay Server for updates

---

## 4. Data Flow & Protocols

| Step | Source                          | Transport           | Payload                          |
|------|---------------------------------|---------------------|----------------------------------|
| 1    | WebDisplay Server → Displays    | HTTPS               | Web app, layouts, configs        |
| 2    | ODIS → Vehicle Central Computer | gRPC / REST / WS    | Data poll/fetch requests         |
| 3    | Vehicle Central Computer → ODIS | gRPC / REST / WS    | Vehicle & journey status data    |
| 4    | ODIS → Display Clients          | WebSocket           | Real-time vehicle/journey events |
| 5    | Display Clients → ODIS          | WebSocket ping/pong | Heartbeat / health checks        |
| 6    | WebDisplay Server → Displays    | WebSocket (optional)| Layout/config updates            |

---

## 5. Configuration & Layout Management

- **WebDisplay Server Configuration**  
  - `layoutId` (string)  
  - `theme` (light/dark)  
  - `regionSettings` (language, units)
  - `dataSources` (includes ODIS WebSocket URL)
  - `widgets` (UI components to display)
- **Dynamic Reload**  
  - Clients must support hot‑reload on config change  
  - WebDisplay Server can push configuration updates via WebSocket

---

## 6. Security & Reliability

- **Authentication**  
  - TLS mutual auth between ODIS and CIS  
  - API key or JWT for display clients connecting to ODIS
- **Authorization**  
  - Per‑display access control on ODIS
- **Resilience**  
  - Automatic reconnect with exponential back‑off  
  - Local cache of last known vehicle data

---

## 7. Monitoring & Error Handling

- **Health Endpoints**  
  - `/health/live`  
  - `/health/ready`  
- **Metrics**  
  - Active connections  
  - Message rate (in/out)  
- **Logging**  
  - Ingestion errors, malformed messages  
  - Client connect/disconnect events  

---

## 8. Dependencies & Deployment

- **Runtimes**  
  - Node.js ≥ 18.x or JVM ≥ 11 (TBD)  
- **Datastores**  
  - In‑memory (no persistent storage)  
- **Containerization**  
  - Dockerfile + Kubernetes manifest  

---

## 9. Next Steps

1. Finalize transport protocols and data schemas  
2. Define precise JSON schema for ODIS messages
3. Implement proof‑of‑concept with one display layout  
4. Write integration tests against mock CIS feed  

---

*End of first draft*  
